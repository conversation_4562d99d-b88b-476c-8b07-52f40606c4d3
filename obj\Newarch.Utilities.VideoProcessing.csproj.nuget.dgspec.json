{"format": 1, "restore": {"D:\\GIT\\Newarch.Utilities.VideoProcessing\\Newarch.Utilities.VideoProcessing.csproj": {}}, "projects": {"D:\\GIT\\Newarch.Utilities.VideoProcessing\\Newarch.Utilities.VideoProcessing.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\GIT\\Newarch.Utilities.VideoProcessing\\Newarch.Utilities.VideoProcessing.csproj", "projectName": "Newarch.Utilities.VideoProcessing", "projectPath": "D:\\GIT\\Newarch.Utilities.VideoProcessing\\Newarch.Utilities.VideoProcessing.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\GIT\\Newarch.Utilities.VideoProcessing\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Azure.Storage.Blobs": {"target": "Package", "version": "[12.23.0, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}}}