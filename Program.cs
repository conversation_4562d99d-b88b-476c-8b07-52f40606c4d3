using Newtonsoft.Json;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using System.Diagnostics;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}

app.UseHttpsRedirection();

app.MapPost("", async (HttpContext context, ILoggerFactory loggerFactory) =>
{
    var logger = loggerFactory.CreateLogger("EncodeVideo");
    logger.LogInformation("Video encoding API started.");

    // Read and deserialize request body
    string requestBody = await new StreamReader(context.Request.Body).ReadToEndAsync();
    var requestData = JsonConvert.DeserializeObject<VideoRequest>(requestBody);

    if (string.IsNullOrEmpty(requestData?.ConnectionString))
    {
        context.Response.StatusCode = StatusCodes.Status400BadRequest;
        await context.Response.WriteAsync("Please provide both connectionString in the request body.");
        return;
    }

     if (string.IsNullOrEmpty(requestData?.ThumbUrl) )
    {
        context.Response.StatusCode = StatusCodes.Status400BadRequest;
        await context.Response.WriteAsync("Please provide both thumbUrl in the request body.");
        return;
    }

    if (string.IsNullOrEmpty(requestData?.SourceUrl) || string.IsNullOrEmpty(requestData?.DestUrl))
    {
        context.Response.StatusCode = StatusCodes.Status400BadRequest;
        await context.Response.WriteAsync("Please provide both sourceUrl and destUrl in the request body.");
        return;
    }

    try
    {
        // Blob storage connection
        var blobServiceClient = new BlobServiceClient(requestData.ConnectionString);
        var (srcContainerName, srcBlobName) = GetContainerAndBlobNames(requestData.SourceUrl);
        var (thumbContainerName, thumbBlobName) = GetContainerAndBlobNames(requestData.ThumbUrl);
        var (destContainerName, destBlobName) = GetContainerAndBlobNames(requestData.DestUrl);

        // Temporary file paths
        string tempVideoPath = Path.GetTempFileName();
        string compressedVideoPath = $"{tempVideoPath}_{Guid.NewGuid()}.mp4";
        string thumbnailPath = $"{tempVideoPath}_{Guid.NewGuid()}.jpg";

        // Download the source video
        logger.LogInformation("Downloading video from source blob...");
        var srcBlobContainerClient = blobServiceClient.GetBlobContainerClient(srcContainerName);
        var srcBlobClient = srcBlobContainerClient.GetBlobClient(srcBlobName);
        await srcBlobClient.DownloadToAsync(tempVideoPath);

        // FFmpeg path
        string ffmpegPath = Path.Combine(Directory.GetCurrentDirectory(), "ffmpeg/ffmpeg.exe");
        logger.LogInformation($"FFmpeg path: {ffmpegPath}");
        if (!File.Exists(ffmpegPath))
        {
            throw new FileNotFoundException($"FFmpeg executable not found at {ffmpegPath}.");
        }

        // Compress video
        logger.LogInformation("Compressing video...");
        await RunFFmpeg(ffmpegPath, $"-i \"{tempVideoPath}\" -vf scale=-1:720 -c:v libx264 -preset fast -crf 23 -c:a aac -b:a 128k \"{compressedVideoPath}\"");

        // Generate thumbnail
        logger.LogInformation("Generating thumbnail...");
        await RunFFmpeg(ffmpegPath, $"-i \"{compressedVideoPath}\" -vf thumbnail -frames:v 1 \"{thumbnailPath}\"");

        // Upload compressed video
        logger.LogInformation("Uploading compressed video...");
        var destBlobContainerClient = blobServiceClient.GetBlobContainerClient(destContainerName);
        var destVideoBlobClient = destBlobContainerClient.GetBlobClient(destBlobName);
        await UploadFileToBlob(destVideoBlobClient, compressedVideoPath, "video/mp4");

        // Upload thumbnail
        logger.LogInformation("Uploading thumbnail...");
        var thumbBlobContainerClient = blobServiceClient.GetBlobContainerClient(thumbContainerName);
        // string thumbnailBlobName = Path.GetFileNameWithoutExtension(destBlobName) + "_thumb.jpg";
        var destThumbnailBlobClient = thumbBlobContainerClient.GetBlobClient(thumbBlobName);
        await UploadFileToBlob(destThumbnailBlobClient, thumbnailPath, "image/jpeg");
        

        // Return response
        var response = new
        {
            VideoUrl = destVideoBlobClient.Uri,
            ThumbUrl = destThumbnailBlobClient.Uri
        };

        context.Response.ContentType = "application/json";
        await context.Response.WriteAsync(JsonConvert.SerializeObject(response));
    }
    catch (Exception ex)
    {
        logger.LogError($"Error processing video: {ex.Message}");
        context.Response.StatusCode = StatusCodes.Status500InternalServerError;
        await context.Response.WriteAsync("Error processing video.");
    }
});


async Task RunFFmpeg(string ffmpegPath, string arguments)
{
    var process = new Process
    {
        StartInfo = new ProcessStartInfo
        {
            FileName = ffmpegPath,
            Arguments = arguments,
            RedirectStandardError = true,
            RedirectStandardOutput = true,
            UseShellExecute = false,
            CreateNoWindow = true
        }
    };

    process.Start();
    string errorOutput = await process.StandardError.ReadToEndAsync();
    await process.WaitForExitAsync();

    if (process.ExitCode != 0)
    {
        throw new Exception($"FFmpeg failed: {errorOutput}");
    }
}

async Task UploadFileToBlob(BlobClient blobClient, string filePath, string contentType)
{
    await using var fileStream = File.OpenRead(filePath);
    var headers = new BlobHttpHeaders { ContentType = contentType };
    await blobClient.UploadAsync(fileStream, headers);
}

(string ContainerName, string BlobName) GetContainerAndBlobNames(string blobUrl)
{
    var uri = new Uri(blobUrl);
    var segments = uri.AbsolutePath.Trim('/').Split('/');
    return (segments[0], string.Join("/", segments.Skip(1)));
}

app.Run();

public class VideoRequest
{
    public string ConnectionString { get; set; }
    public string SourceUrl { get; set; }
    public string ThumbUrl { get; set; }
    public string DestUrl { get; set; }
}
